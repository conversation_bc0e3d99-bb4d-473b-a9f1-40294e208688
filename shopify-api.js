const axios = require('axios');
const config = require('./config');

class ShopifyAPI {
  constructor() {
    this.config = config;
    this.headers = {
      'X-Shopify-Access-Token': this.config.accessToken,
      'Content-Type': 'application/json'
    };
  }

  // GraphQL query method
  async graphqlQuery(query, variables = {}) {
    try {
      const response = await axios.post(this.config.graphqlEndpoint, {
        query,
        variables
      }, {
        headers: this.headers
      });

      if (response.data.errors) {
        console.error('GraphQL Errors:', response.data.errors);
        throw new Error('GraphQL query failed');
      }

      return response.data;
    } catch (error) {
      console.error('API Request Error:', error.response?.data || error.message);
      throw error;
    }
  }

  // Get all products with detailed information
  async getProducts(first = 10) {
    const query = `
      query getProducts($first: Int!) {
        products(first: $first) {
          edges {
            node {
              id
              title
              handle
              description
              productType
              vendor
              status
              createdAt
              updatedAt
              tags
              images(first: 5) {
                edges {
                  node {
                    id
                    url
                    altText
                  }
                }
              }
              variants(first: 10) {
                edges {
                  node {
                    id
                    title
                    price
                    compareAtPrice
                    sku
                    inventoryQuantity
                    weight
                    weightUnit
                  }
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            hasPreviousPage
          }
        }
      }
    `;

    return await this.graphqlQuery(query, { first });
  }

  // Search for products by title (useful for finding cowboy coffee)
  async searchProducts(searchTerm, first = 10) {
    const query = `
      query searchProducts($query: String!, $first: Int!) {
        products(first: $first, query: $query) {
          edges {
            node {
              id
              title
              handle
              description
              productType
              vendor
              status
              tags
              images(first: 3) {
                edges {
                  node {
                    id
                    url
                    altText
                  }
                }
              }
              variants(first: 5) {
                edges {
                  node {
                    id
                    title
                    price
                    compareAtPrice
                    sku
                    inventoryQuantity
                  }
                }
              }
            }
          }
        }
      }
    `;

    return await this.graphqlQuery(query, { 
      query: `title:*${searchTerm}*`,
      first 
    });
  }

  // Get a specific product by ID
  async getProduct(productId) {
    const query = `
      query getProduct($id: ID!) {
        product(id: $id) {
          id
          title
          handle
          description
          productType
          vendor
          status
          createdAt
          updatedAt
          tags
          images(first: 10) {
            edges {
              node {
                id
                url
                altText
              }
            }
          }
          variants(first: 20) {
            edges {
              node {
                id
                title
                price
                compareAtPrice
                sku
                inventoryQuantity
                weight
                weightUnit
                selectedOptions {
                  name
                  value
                }
              }
            }
          }
          options {
            id
            name
            values
          }
        }
      }
    `;

    return await this.graphqlQuery(query, { id: productId });
  }

  // Test API connection
  async testConnection() {
    try {
      const query = `
        query {
          shop {
            name
            email
            domain
            currencyCode
          }
        }
      `;
      
      const result = await this.graphqlQuery(query);
      return result.data.shop;
    } catch (error) {
      throw new Error(`API connection test failed: ${error.message}`);
    }
  }
}

module.exports = ShopifyAPI;
