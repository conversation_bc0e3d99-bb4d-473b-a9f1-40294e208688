const ShopifyAPI = require('./shopify-api');

async function testShopifyAPI() {
  const api = new ShopifyAPI();
  
  console.log('🧪 Testing Shopify API Connection...\n');
  
  try {
    // Test 1: Connection test
    console.log('1️⃣ Testing API connection...');
    const shopInfo = await api.testConnection();
    console.log('✅ Connection successful!');
    console.log('Shop Info:', {
      name: shopInfo.name,
      domain: shopInfo.domain,
      email: shopInfo.email,
      currency: shopInfo.currencyCode
    });
    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Get all products
    console.log('2️⃣ Fetching all products...');
    const allProducts = await api.getProducts(20);
    console.log(`✅ Found ${allProducts.data.products.edges.length} products`);
    
    // Display basic product info
    allProducts.data.products.edges.forEach((edge, index) => {
      const product = edge.node;
      console.log(`${index + 1}. ${product.title}`);
      console.log(`   - Handle: ${product.handle}`);
      console.log(`   - Type: ${product.productType}`);
      console.log(`   - Status: ${product.status}`);
      console.log(`   - Variants: ${product.variants.edges.length}`);
      if (product.variants.edges.length > 0) {
        console.log(`   - Price: $${product.variants.edges[0].node.price}`);
      }
      console.log('');
    });
    console.log('='.repeat(50) + '\n');

    // Test 3: Search for coffee products
    console.log('3️⃣ Searching for coffee products...');
    const coffeeProducts = await api.searchProducts('coffee', 10);
    console.log(`✅ Found ${coffeeProducts.data.products.edges.length} coffee products`);
    
    coffeeProducts.data.products.edges.forEach((edge, index) => {
      const product = edge.node;
      console.log(`${index + 1}. ${product.title}`);
      console.log(`   - Handle: ${product.handle}`);
      console.log(`   - Description: ${product.description?.substring(0, 100)}...`);
      console.log(`   - Tags: ${product.tags.join(', ')}`);
      console.log('');
    });
    console.log('='.repeat(50) + '\n');

    // Test 4: Search specifically for cowboy coffee
    console.log('4️⃣ Searching specifically for "cowboy coffee"...');
    const cowboyProducts = await api.searchProducts('cowboy', 5);
    console.log(`✅ Found ${cowboyProducts.data.products.edges.length} products matching "cowboy"`);
    
    if (cowboyProducts.data.products.edges.length > 0) {
      console.log('\n🤠 COWBOY COFFEE PRODUCTS FOUND:');
      cowboyProducts.data.products.edges.forEach((edge, index) => {
        const product = edge.node;
        console.log(`\n${index + 1}. ${product.title}`);
        console.log(`   - ID: ${product.id}`);
        console.log(`   - Handle: ${product.handle}`);
        console.log(`   - Product Type: ${product.productType}`);
        console.log(`   - Vendor: ${product.vendor}`);
        console.log(`   - Status: ${product.status}`);
        console.log(`   - Description: ${product.description}`);
        console.log(`   - Tags: ${product.tags.join(', ')}`);
        
        // Show variants
        console.log(`   - Variants (${product.variants.edges.length}):`);
        product.variants.edges.forEach((variantEdge, vIndex) => {
          const variant = variantEdge.node;
          console.log(`     ${vIndex + 1}. ${variant.title}`);
          console.log(`        - Price: $${variant.price}`);
          console.log(`        - SKU: ${variant.sku || 'N/A'}`);
          console.log(`        - Inventory: ${variant.inventoryQuantity || 'N/A'}`);
        });
        
        // Show images
        if (product.images.edges.length > 0) {
          console.log(`   - Images (${product.images.edges.length}):`);
          product.images.edges.forEach((imageEdge, iIndex) => {
            console.log(`     ${iIndex + 1}. ${imageEdge.node.url}`);
          });
        }
      });
    } else {
      console.log('❌ No cowboy coffee products found. Let\'s try a broader search...');
      
      // Try searching for just "cowboy"
      const broadSearch = await api.searchProducts('coffee', 20);
      console.log(`\n📋 All coffee products (${broadSearch.data.products.edges.length} found):`);
      broadSearch.data.products.edges.forEach((edge, index) => {
        console.log(`${index + 1}. ${edge.node.title} (${edge.node.handle})`);
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testShopifyAPI();
