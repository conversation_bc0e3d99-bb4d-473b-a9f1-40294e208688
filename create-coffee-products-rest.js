const ShopifyAPI = require('./shopify-api');

// Your existing selling plan group IDs
const SELLING_PLAN_GROUP_IDS = [
  'gid://shopify/SellingPlanGroup/3002597691', // Weekly subscription
  'gid://shopify/SellingPlanGroup/3002630459', // Monthly Subscription
  'gid://shopify/SellingPlanGroup/3002663227', // Every 3 Weeks Subscription
  'gid://shopify/SellingPlanGroup/3002695995'  // Every 6 Weeks Subscription
];

// Coffee products to create based on markdown descriptions
const COFFEE_PRODUCTS = [
  {
    name: 'Controlled Burn',
    handle: 'controlled-burn',
    roast: 'Dark',
    origin: 'Colombian',
    type: 'Single Origin',
    ingredients: '100% Arabica Coffee',
    description: 'Experience the bold, handcrafted flavor of Big River Controlled Burn from Big River Coffee. Sourced from the highlands of Colombia, this specialty quality coffee is roasted to a dark profile, delivering a robust and intense taste with a rustic charm.',
    flavorProfile: 'robust and intense taste with a rustic charm'
  },
  {
    name: 'Fairway Fuel',
    handle: 'fairway-fuel',
    roast: 'Medium',
    origin: 'Colombian',
    type: 'Single Origin',
    ingredients: '100% Arabica Coffee',
    description: 'Experience the invigorating, handcrafted flavor of Big River Fairway Fuel from Big River Coffee. Sourced from the highlands of Colombia, this specialty quality coffee is roasted to a medium profile, offering a vibrant and full-bodied taste.',
    flavorProfile: 'vibrant and full-bodied taste'
  },
  {
    name: 'Hazelnut',
    handle: 'hazelnut',
    roast: 'Medium',
    origin: 'Flavored',
    type: 'Flavored',
    ingredients: '100% Arabica Coffee with Natural Hazelnut Flavoring',
    description: 'Experience the delightful, handcrafted flavor of Big River Hazelnut from Big River Coffee. Infused with rich hazelnut essence, this specialty quality flavored coffee is roasted to a medium profile, delivering a smooth and aromatic taste.',
    flavorProfile: 'smooth and aromatic taste with rich hazelnut essence'
  },
  {
    name: 'Morning Mist',
    handle: 'morning-mist',
    roast: 'Medium',
    origin: 'Nicaraguan',
    type: 'Single Origin',
    ingredients: '100% Arabica Coffee',
    description: 'Experience the refreshing, handcrafted flavor of Big River Morning Mist from Big River Coffee. Sourced from the highlands of Nicaragua, this specialty quality coffee is roasted to a medium profile, offering a smooth and balanced taste to start your day.',
    flavorProfile: 'smooth and balanced taste'
  },
  {
    name: 'Natural Decaf',
    handle: 'natural-decaf',
    roast: 'Medium',
    origin: 'Blend',
    type: 'Decaffeinated Blend',
    ingredients: '100% Arabica Coffee (Naturally Decaffeinated)',
    description: 'Experience the smooth, handcrafted flavor of Big River Natural Decaf from Big River Coffee. Crafted from a premium blend, this specialty quality decaffeinated coffee is roasted to a medium profile, offering a mild and balanced taste.',
    flavorProfile: 'mild and balanced taste without caffeine'
  },
  {
    name: 'River Bend',
    handle: 'river-bend',
    roast: 'Medium',
    origin: 'Nicaraguan',
    type: 'Single Origin',
    ingredients: '100% Arabica Coffee',
    description: 'Experience the rich, handcrafted flavor of Big River River Bend from Big River Coffee. Sourced from the highlands of Nicaragua, this specialty quality coffee is roasted to a perfect medium profile, offering a balanced and smooth taste.',
    flavorProfile: 'balanced and smooth taste'
  },
  {
    name: 'Vanilla',
    handle: 'vanilla',
    roast: 'Medium',
    origin: 'Flavored',
    type: 'Flavored',
    ingredients: '100% Arabica Coffee with Natural Vanilla Flavoring',
    description: 'Experience the sweet, handcrafted flavor of Big River Vanilla from Big River Coffee. Infused with rich vanilla essence, this specialty quality flavored coffee is roasted to a medium profile, delivering a smooth and aromatic taste.',
    flavorProfile: 'smooth and aromatic taste with rich vanilla essence'
  },
  {
    name: 'Wrangler Roast',
    handle: 'wrangler-roast',
    roast: 'Dark',
    origin: 'Nicaraguan',
    type: 'Single Origin',
    ingredients: '100% Arabica Coffee',
    description: 'Experience the bold, handcrafted flavor of Big River Wrangler Roast from Big River Coffee. Sourced from the highlands of Nicaragua, this specialty quality coffee is roasted to a dark profile, delivering a robust and intense taste.',
    flavorProfile: 'robust and intense taste'
  }
];

function createCoffeeProductDataREST(coffee) {
  const basePrice12oz = '19.99';
  const basePrice2lb = '31.99';
  const basePrice5lb = '66.99';
  
  // Create SKU prefix from coffee name (first 3 letters)
  const skuPrefix = coffee.name.replace(/[^a-zA-Z]/g, '').substring(0, 3).toUpperCase();
  
  // Create product title
  const title = coffee.roast ? `${coffee.name} - ${coffee.roast} Roast` : coffee.name;
  
  // Create comprehensive description following Cowboy Coffee format
  const fullDescription = `Big River ${coffee.name}

${coffee.description} Available in both ground and whole bean options, this ${coffee.type === 'Flavored' ? 'premium coffee' : '100% Arabica coffee'} is ideal for coffee lovers seeking a premium brew.

Product Details:
• ${coffee.origin === 'Flavored' ? 'Type' : 'Origin'}: ${coffee.origin}
• Roast: ${coffee.roast}
• Ingredients: ${coffee.ingredients}

How to Enjoy:
For the best experience, use 1-1½ tablespoons of coffee per 6 fluid ounces of filtered water. Adjust to your personal taste and follow your coffee maker's brewing instructions.

About the Brand:
Handcrafted with care and distributed by Big River Coffee, Pittsburgh, PA. Scan the QR code on the bag for more details.`;

  // Create appropriate tags
  const tags = ['coffee', coffee.roast.toLowerCase().replace(/\s+/g, '-'), 'arabica', 'premium'];
  if (coffee.origin !== 'Flavored' && coffee.origin !== 'Blend') {
    tags.push(coffee.origin.toLowerCase().replace(/\s+/g, '-'));
  }
  if (coffee.type === 'Flavored') {
    tags.push('flavored');
  }
  if (coffee.type === 'Decaffeinated Blend') {
    tags.push('decaf', 'decaffeinated');
  }
  
  return {
    title: title,
    handle: coffee.handle,
    body_html: fullDescription.replace(/\n/g, '<br>'),
    product_type: 'Coffee',
    vendor: 'Big River Coffee',
    status: 'draft', // Create as drafts as requested
    tags: tags.join(', '),
    
    options: [
      {
        name: 'Size',
        values: ['12oz', '2lb', '5lb']
      },
      {
        name: 'Grind',
        values: ['whole bean', 'ground']
      }
    ],
    variants: [
      {
        option1: '12oz',
        option2: 'whole bean',
        price: basePrice12oz,
        sku: `BRMR-${skuPrefix}WB-O-12`,
        inventory_management: 'shopify',
        inventory_quantity: 100,
        requires_shipping: true,
        taxable: true
      },
      {
        option1: '12oz',
        option2: 'ground',
        price: basePrice12oz,
        sku: `BRMR-${skuPrefix}G-O-12`,
        inventory_management: 'shopify',
        inventory_quantity: 100,
        requires_shipping: true,
        taxable: true
      },
      {
        option1: '2lb',
        option2: 'whole bean',
        price: basePrice2lb,
        sku: `BRMR-${skuPrefix}WB-O-2`,
        inventory_management: 'shopify',
        inventory_quantity: 50,
        requires_shipping: true,
        taxable: true
      },
      {
        option1: '2lb',
        option2: 'ground',
        price: basePrice2lb,
        sku: `BRMR-${skuPrefix}G-O-2`,
        inventory_management: 'shopify',
        inventory_quantity: 50,
        requires_shipping: true,
        taxable: true
      },
      {
        option1: '5lb',
        option2: 'whole bean',
        price: basePrice5lb,
        sku: `BRMR-${skuPrefix}WB-O-5`,
        inventory_management: 'shopify',
        inventory_quantity: 25,
        requires_shipping: true,
        taxable: true
      }
    ]
  };
}

async function createAllCoffeeProductsREST() {
  const api = new ShopifyAPI();
  
  console.log('☕ Creating all Big River Coffee products as drafts using REST API...\n');
  console.log(`📋 Products to create: ${COFFEE_PRODUCTS.length}`);
  console.log(`🔄 Each product will be associated with ${SELLING_PLAN_GROUP_IDS.length} subscription options\n`);
  
  const results = [];
  
  for (let i = 0; i < COFFEE_PRODUCTS.length; i++) {
    const coffee = COFFEE_PRODUCTS[i];
    
    try {
      console.log(`${i + 1}/${COFFEE_PRODUCTS.length} Creating: ${coffee.name}...`);
      
      // Create product data for REST API
      const productData = createCoffeeProductDataREST(coffee);
      
      // Create the product using REST API
      const result = await api.createProductREST(productData);
      
      console.log(`✅ Created: ${result.product.title}`);
      console.log(`   - Product ID: ${result.product.id}`);
      console.log(`   - Handle: ${result.product.handle}`);
      console.log(`   - Status: ${result.product.status} (Draft)`);
      console.log(`   - Variants: ${result.product.variants.length}`);
      
      // Now associate with selling plan groups
      const productGid = `gid://shopify/Product/${result.product.id}`;
      
      console.log(`   - Adding to ${SELLING_PLAN_GROUP_IDS.length} subscription plans...`);
      for (const groupId of SELLING_PLAN_GROUP_IDS) {
        try {
          await api.addProductToSellingPlanGroup(groupId, productGid);
        } catch (error) {
          console.log(`     ⚠️  Warning: Could not add to selling plan ${groupId}: ${error.message}`);
        }
      }
      
      results.push({
        success: true,
        coffee: coffee.name,
        productId: result.product.id,
        handle: result.product.handle
      });
      
      console.log('   ✅ Subscription plans added successfully');
      console.log('');
      
      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1500));
      
    } catch (error) {
      console.error(`❌ Failed to create ${coffee.name}:`, error.message);
      results.push({
        success: false,
        coffee: coffee.name,
        error: error.message
      });
      console.log('');
    }
  }
  
  // Summary
  console.log('='.repeat(60));
  console.log('📊 CREATION SUMMARY');
  console.log('='.repeat(60));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successfully created: ${successful.length} products`);
  console.log(`❌ Failed to create: ${failed.length} products`);
  
  if (successful.length > 0) {
    console.log('\n🎉 Successfully Created Products:');
    successful.forEach((result, index) => {
      console.log(`${index + 1}. ${result.coffee} (${result.handle})`);
      console.log(`   Product ID: ${result.productId}`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed Products:');
    failed.forEach((result, index) => {
      console.log(`${index + 1}. ${result.coffee}: ${result.error}`);
    });
  }
  
  console.log('\n💡 Next Steps:');
  console.log('1. Add product images from the all_images folder');
  console.log('2. Review and edit product descriptions if needed');
  console.log('3. Change status from DRAFT to ACTIVE when ready');
  console.log('4. Test subscription functionality on your storefront');
  
  return results;
}

// Run if called directly
if (require.main === module) {
  createAllCoffeeProductsREST()
    .then(results => {
      console.log('\n🏁 All done!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = {
  createAllCoffeeProductsREST,
  COFFEE_PRODUCTS,
  SELLING_PLAN_GROUP_IDS
};
